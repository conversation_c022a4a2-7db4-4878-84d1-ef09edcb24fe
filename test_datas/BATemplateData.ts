export interface FormParams {
  /** 模板名称，最多20字符，必填 */
  name: string;
  /** 模板类型，如"中医病案"，必填 */
  type: string;
  /** 诊断内容，最多20字符，必填 */
  diagnosis: string;
  /** 模板说明，最多50字符，必填 */
  description: string;
  /** 病情主诉，最多200字符，非必填 */
  chiefComplaint?: string;
  /** 体格检查，最多200字符，非必填 */
  physicalExam?: string;
  /** 现病史，最多200字符，非必填 */
  presentIllness?: string;
  /** 既往史，最多200字符，非必填 */
  pastHistory?: string;
  /** 个人史，最多200字符，非必填 */
  personalHistory?: string;
  /** 家族史，最多200字符，非必填 */
  familyHistory?: string;
  /** 过敏史，最多200字符，非必填 */
  allergicHistory?: string;
  /** 婚育史，最多200字符，非必填 */
  marriageHistory?: string;
  /** 望闻问切（疾病症状），最多200字符，非必填 */
  symptoms?: string;
}

export interface SearchParams {
  /** 模板编号 */
  templateNo?: string;
  /** 模板名称 */
  name?: string;
  /** 模板分类 */
  type?: string;
  /** 诊断 */
  diagnosis?: string;
  /** 说明 */
  description?: string;
  /** 创建人 */
  creator?: string;
  /** 创建时间范围 */
  createTimeRange?: {
    start: string;
    end: string;
  };
}

const tag = Date.now();

export const emptyData: FormParams = {
  name: '',
  type: '',
  diagnosis: '',
  description: '',
  chiefComplaint: '',
  physicalExam: '',
  presentIllness: '',
  pastHistory: '',
  personalHistory: '',
  familyHistory: '',
  allergicHistory: '',
  marriageHistory: '',
  symptoms: ''
};

export const fullAddData: FormParams = {
  name: '测试模板' + tag,
  type: '中医病案',
  diagnosis: '测试诊断',
  description: '测试说明',
  chiefComplaint: '患者主诉头痛、发热',
  physicalExam: '体温38.5℃，血压120/80mmHg',
  presentIllness: '患者3天前开始出现头痛、发热症状',
  pastHistory: '无重大疾病史',
  personalHistory: '无吸烟、饮酒史',
  familyHistory: '无家族遗传病史',
  allergicHistory: '无药物过敏史',
  marriageHistory: '已婚已育',
  symptoms: '舌红苔薄白，脉浮数'
};

export const fullEditData: FormParams = {
  name: '修改后模板' + tag,
  type: '中医病案',
  diagnosis: '修改后诊断',
  description: '修改后说明',
  chiefComplaint: '患者主诉咳嗽、咳痰',
  physicalExam: '体温37.2℃，血压118/78mmHg',
  presentIllness: '患者5天前开始出现咳嗽、咳痰症状',
  pastHistory: '有慢性支气管炎病史',
  personalHistory: '有吸烟史20年',
  familyHistory: '父亲有哮喘病史',
  allergicHistory: '对青霉素过敏',
  marriageHistory: '已婚已育',
  symptoms: '舌淡红苔白腻，脉滑数'
};

export const reqAddData: FormParams = {
  name: '必填模板' + tag,
  type: '中医病案',
  diagnosis: '必填诊断',
  description: '必填说明'
};

export const reqEditData: FormParams = {
  name: '修改后模板' + tag,
  type: '中医病案',
  diagnosis: '修改后_必填诊断',
  description: '修改后_必填说明'
};

export const searchData = {
  name: '测试模板',
  type: '中医病案',
  diagnosis: '测试诊断',
  description: '测试说明',
  creator: '测试用户',
  createTimeRange: {
    start: '2024-01-01',
    end: '2024-12-31'
  }
};

