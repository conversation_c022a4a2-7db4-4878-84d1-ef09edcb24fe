/**
 * 角色表单参数接口
 */
export interface RoleFormParams {
  /** 角色ID，系统自动生成，不需要填写 */
  roleId?: string;
  /** 角色名称，必填 */
  roleName: string;
  /** 数据权限，必填 */
  dataPermission: string;
}

/**
 * 角色搜索参数接口
 */
export interface RoleSearchParams {
  /** 所属机构 */
  organization?: string;
  /** 角色名称 */
  roleName?: string;
}

// 空数据
export const emptyData: RoleFormParams = {
  roleName: '',
  dataPermission: '',
};

// 基本角色测试数据
export const basicRoleData: RoleFormParams = {
  roleName: `测试test角色`,
  dataPermission: '查看全部',
};

// 编辑角色测试数据
export const editRoleData: RoleFormParams = {
  roleName: `编辑后test角色`,
  dataPermission: '查看本部门',
};

// 搜索测试数据
export const searchTestData: RoleSearchParams = {
  organization: '',
  roleName: ''
};
