/**
 * 讲师搜索参数接口
 */
export interface LecturerSearchParams {
  lecturerId?: string;         // 讲师ID
  lecturerName?: string;       // 讲师姓名
  phone?: string;              // 手机号
  region?: string;             // 所在地区
  organization?: string;       // 所属机构
  hospital?: string;           // 坐诊医院
  department?: string;         // 所在科室
  title?: string;              // 职称
  lecturerCategory?: string;   // 讲师分类
  hasAccount?: string;         // 有无账号
  lecturerAccount?: string;    // 讲师账号
  createTimeStart?: string;    // 创建时间开始
  createTimeEnd?: string;      // 创建时间结束
}

/**
 * 讲师表单参数接口
 */
export interface LecturerFormParams {
  lecturerName?: string;       // 讲师姓名
  phone?: string;              // 手机号
  region?: string;             // 所在地区
  organization?: string;       // 所属机构
  hospital?: string;           // 坐诊医院
  department?: string;         // 所在科室
  title?: string;              // 职称
  lecturerCategory?: string;   // 讲师分类
  hasAccount?: string;         // 有无账号
  lecturerAccount?: string;    // 讲师账号
  // TODO: 根据实际表单字段补充更多参数
}

/**
 * 空数据用于测试表单验证
 */
export const emptyData: LecturerFormParams = {};

/**
 * 基本讲师数据
 */
export const basicLecturerData: LecturerFormParams = {
  lecturerName: '测试讲师01',
  phone: '***********',
  region: '北京市',
  organization: '演示机构（勿动）',
  hospital: '演示单位',
  department: '内科',
  title: '主治医师',
  lecturerCategory: '名老中医',
  hasAccount: '有',
  lecturerAccount: 'test_lecturer_001'
};

/**
 * 编辑讲师数据
 */
export const editLecturerData: LecturerFormParams = {
  lecturerName: '测试讲师01_编辑',
  phone: '***********',
  region: '上海市',
  organization: '演示机构（勿动）',
  hospital: '演示单位',
  department: '外科',
  title: '副主任医师',
  lecturerCategory: '名老中医',
  hasAccount: '有',
  lecturerAccount: 'test_lecturer_001_edit'
};

/**
 * 搜索测试数据
 */
export const searchData: LecturerSearchParams = {
  lecturerId: 'L25050007',
  lecturerName: '讲师01',
  phone: '***********',
  region: '北京市',
  organization: '演示机构（勿动）',
  hospital: '演示单位',
  department: '其他科室',
  title: '主治医师',
  lecturerCategory: '名老中医'
}; 