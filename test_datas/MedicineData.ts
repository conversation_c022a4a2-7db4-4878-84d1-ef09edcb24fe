/**
 * 药品表单参数接口
 */
export interface MedicineFormParams {
  /** 药品名称，必填 */
  name: string;
  /** 药品类型，如"中草药"或"中成药"，必填 */
  type: string;
  /** 助记简称，非必填 */
  shortName?: string;
  /** 包装单位，必填 */
  unit: string;
  /** 规格，非必填 */
  specification?: string;
  /** 剂型，非必填 */
  dosageForm?: string;
  /** 生产厂家，非必填 */
  manufacturer?: string;
  /** 产地，非必填 */
  origin?: string;
  /** 零售价，必填 */
  retailPrice: string;
  /** 成本价，非必填 */
  costPrice?: string;
  /** 过期预警时间（天），非必填 */
  expiryWarningDays?: string;
  /** 库存预警数量，非必填 */
  stockWarningAmount?: string;
  /** 库存数量，非必填 */
  stockAmount?: string;
  /** 状态，如"上架"或"下架"，非必填 */
  status?: string;
}

/**
 * 药品搜索参数接口
 */
export interface MedicineSearchParams {
  /** 药品编号 */
  medicineNo?: string;
  /** 药品名称 */
  medicineName?: string;
  /** 创建日期范围 */
  createDateRange?: {
    start: string;
    end: string;
  };
}

// 使用固定前缀和随机后缀，便于测试用例的独立执行
const testPrefix = 'MedTest_';
const randomSuffix = Math.floor(Math.random() * 1000).toString().padStart(3, '0');

// 空数据
export const emptyData: MedicineFormParams = {
  name: '',
  type: '',
  specification: '',
  manufacturer: '',
  retailPrice: '',
  unit: '',
  stockAmount: '',
  status: ''
};

// 中草药测试数据
export const herbMedicineData: MedicineFormParams = {
  name: `Test_中草药`,
  type: '中草药',
  shortName: '草药简称',
  unit: '段(选)',
  specification: '100g/包',
  dosageForm: '片剂',
  manufacturer: '测试药厂',
  origin: '北京',
  retailPrice: '10.00',
  costPrice: '8.00',
  expiryWarningDays: '30',
  stockWarningAmount: '100',
  stockAmount: '1000',
  status: '上架'
};

// 中成药测试数据
export const patentMedicineData: MedicineFormParams = {
  name: `Test_中成药`,
  type: '中成药',
  shortName: '成药简称',
  unit: '段(选)',
  specification: '100片/盒',
  dosageForm: '片剂',
  manufacturer: '测试制药厂',
  origin: '上海',
  retailPrice: '20.00',
  costPrice: '15.00',
  expiryWarningDays: '60',
  stockWarningAmount: '50',
  stockAmount: '500',
  status: '上架'
};

// 编辑中草药测试数据
export const editHerbMedicineData: MedicineFormParams = {
  name: `Test_编辑中草药`,
  type: '中草药',
  shortName: '编辑草药',
  unit: '个(统)',
  specification: '200g/包',
  dosageForm: '粉剂',
  manufacturer: '更新药厂',
  origin: '广州',
  retailPrice: '15.00',
  costPrice: '12.00',
  expiryWarningDays: '45',
  stockWarningAmount: '150',
  stockAmount: '2000',
  status: '上架'
};

// 编辑中成药测试数据
export const editPatentMedicineData: MedicineFormParams = {
  name: `Test_编辑中成药`,
  type: '中成药',
  shortName: '编辑成药',
  unit: '个(统)',
  specification: '200片/盒',
  dosageForm: '水剂',
  manufacturer: '更新制药厂',
  origin: '深圳',
  retailPrice: '25.00',
  costPrice: '20.00',
  expiryWarningDays: '90',
  stockWarningAmount: '80',
  stockAmount: '1000',
  status: '上架'
};

// 必填字段测试数据
export const requiredFieldsData: MedicineFormParams = {
  name: `必填药品`,
  type: '中草药',
  unit: '个(统)',
  retailPrice: '5.00'
};

// 搜索测试数据
export const searchTestData: MedicineSearchParams = {
  medicineNo: '', // 将在测试用例中从列表第一行获取
  medicineName: '', // 将在测试用例中从列表第一行获取
  createDateRange: {
    start: new Date().toISOString().split('T')[0], // 当前日期，格式：YYYY-MM-DD
    end: new Date().toISOString().split('T')[0]
  }
};

