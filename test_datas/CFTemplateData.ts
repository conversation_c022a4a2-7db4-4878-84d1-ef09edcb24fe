export interface FormParams {
  /** 模板名称，最多20字符，必填 */
  name: string;
  /** 模板类型，如"中草药处方"或"中成药处方"，必填 */
  type: string;
  /** 诊断内容，最多20字符，必填 */
  diagnosis: string;
  /** 模板说明，最多50字符，非必填 */
  description?: string;

  /** 处方药品列表 */
  medicines?: {
    /** 药品名称 */
    name: string;
    /** 数量 */
    quantity: string;
  }[];

  /** 煎煮方法，最多200字符，非必填 */
  decoction?: string;
  /** 总贴数，最多2字符，非必填 */
  totalDosage?: string;
  /** 用药方法，最多200字符，非必填 */
  usage?: string;
  /** 用药频次，最多200字符，非必填 */
  frequency?: string;
  /** 备注，最多200字符，非必填 */
  remark?: string;
}

export interface SearchParams {
  /** 模板编号 */
  templateNo?: string;
  /** 模板名称 */
  name?: string;
  /** 模板分类 */
  type?: string;
  /** 诊断 */
  diagnosis?: string;
  /** 说明 */
  description?: string;
  /** 创建人 */
  creator?: string;
  /** 创建时间范围 */
  createTimeRange?: {
    start: string;
    end: string;
  };
}

const tag = Date.now();

export const emptyData: FormParams = {
  name: '',
  type: '',
  diagnosis: '',
  description: '',
  medicines: [],
  decoction: '',
  totalDosage: '',
  usage: '',
  frequency: '',
  remark: ''
};

export const detailEmptyData: FormParams = {
  name: '处方模板' + tag,
  type: '中草药处方',
  diagnosis: '测试诊断',
  description: '测试说明',
  medicines: [],
  decoction: '',
  totalDosage: '',
  usage: '',
  frequency: '',
  remark: ''
};

export const fullAddData: FormParams = {
  name: '完整处方' + tag,
  type: '中草药处方',
  diagnosis: '测试诊断',
  description: '测试说明',
  medicines: [
    {
      name: '测试甘草片a',
      quantity: '2'
    },
    {
      name: '测试红叶片b',
      quantity: '3'
    }
  ],
  decoction: '水煎两次，每次半小时',
  totalDosage: '7',
  usage: '早晨空腹服用',
  frequency: '一日三次',
  remark: '孕妇慎用'
};

export const fullEditData: FormParams = {
  name: '编辑处方' + tag,
  type: '中草药处方',
  diagnosis: '修改后诊断',
  description: '修改后说明',
  medicines: [
    {
      name: '测试青霜片c',
      quantity: '4'
    }
  ],
  decoction: '水煎三次，每次四十分钟',
  totalDosage: '10',
  usage: '饮水送服',
  frequency: '一日两次',
  remark: '高血压患者慎用'
};

export const reqAddData: FormParams = {
  name: '必填处方' + tag,
  type: '中草药处方',
  diagnosis: '必填诊断',
  medicines: [
    {
      name: '测试红叶片b',
      quantity: '2'
    }
  ]
};

export const reqEditData: FormParams = {
  name: '编辑必填处方' + tag,
  type: '中草药处方',
  diagnosis: '修改后_必填诊断',
  medicines: [
    {
      name: '测试甘草片a',
      quantity: '3'
    }
  ]
};

export const searchData = {
  name: '处方模板',
  type: '中草药处方',
  diagnosis: '测试诊断',
  description: '测试说明',
  creator: '测试用户',
  createTimeRange: {
    start: '2024-01-01',
    end: '2024-12-31'
  }
};
