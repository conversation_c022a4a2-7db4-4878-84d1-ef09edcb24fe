{"name": "playwright-tcmsp", "version": "1.0.0", "main": "index.js", "scripts": {"test": "playwright test", "test:fat": "cross-env ENVIRONMENT=FAT playwright test", "test:uat": "cross-env ENVIRONMENT=UAT playwright test", "test:prod": "cross-env ENVIRONMENT=PROD playwright test", "test:fat:smoke": "cross-env ENVIRONMENT=FAT playwright test --grep @smoke", "test:uat:smoke": "cross-env ENVIRONMENT=UAT playwright test --grep @smoke", "test:prod:smoke": "cross-env ENVIRONMENT=PROD playwright test --grep @smoke"}, "keywords": [], "author": "", "license": "ISC", "description": "中医药问诊管理系统自动化测试框架", "devDependencies": {"@playwright/test": "^1.53.1", "@types/dotenv": "^8.2.3", "@types/node": "^22.13.14", "cross-env": "^7.0.3", "dotenv": "^16.4.7"}}