import ddddocr
import base64
import os
import sys
import traceback
import io

# 设置标准输出和标准错误的编码为 utf-8
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

def recognize_captcha(base64_image):
    try:
        # 创建 ddddocr 实例，使用离线模式
        print("初始化 ddddocr...", file=sys.stderr)
        ocr = ddddocr.DdddOcr(show_ad=False)  # 简化参数配置
        
        # 解码 base64 图片数据
        print("解码 base64 图片数据...", file=sys.stderr)
        try:
            image_data = base64.b64decode(base64_image)
            print(f"成功解码 base64 数据，长度: {len(image_data)}", file=sys.stderr)
        except Exception as e:
            print(f"base64 解码失败: {str(e)}", file=sys.stderr)
            raise
        
        # 直接使用二进制数据进行识别
        print("开始识别验证码...", file=sys.stderr)
        try:
            result = ocr.classification(image_data)
            print(f"识别结果: {result}", file=sys.stderr)
        except Exception as e:
            print(f"验证码识别失败: {str(e)}", file=sys.stderr)
            raise
        
        # 清理结果（只保留字母和数字）
        result = ''.join(c for c in result if c.isalnum())
        print(f"清理后的结果: {result}", file=sys.stderr)
        
        if not result:
            print("识别结果为空", file=sys.stderr)
            raise ValueError("识别结果为空")
            
        return result
    except Exception as e:
        print(f"验证码识别过程出错: {str(e)}", file=sys.stderr)
        print(traceback.format_exc(), file=sys.stderr)
        sys.exit(1)

if __name__ == '__main__':
    try:
        if len(sys.argv) != 2:
            print("请提供 base64 图片数据作为参数", file=sys.stderr)
            sys.exit(1)
            
        base64_data = sys.argv[1]
        print(f"接收到 base64 数据，长度: {len(base64_data)}", file=sys.stderr)
        
        result = recognize_captcha(base64_data)
        print(result)  # 只输出识别结果，用于捕获
    except Exception as e:
        print(f"程序执行出错: {str(e)}", file=sys.stderr)
        print(traceback.format_exc(), file=sys.stderr)
        sys.exit(1) 