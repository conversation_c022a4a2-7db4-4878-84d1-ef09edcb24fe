import { spawn } from 'child_process';
import { Page, Locator } from '@playwright/test';
import * as path from 'path';

/**
 * 验证码工具类
 * 提供验证码识别相关的功能
 */

/**
 * 云端识别验证码
 * 通过调用Python脚本实现验证码识别
 * 
 * @param page 页面实例
 * @param captchaElement 验证码图片元素
 * @returns Promise<string> 识别结果
 * @throws Error 当无法获取验证码图像或识别失败时抛出错误
 * 
 * @example
 * const captchaElement = page.locator('.captcha-img');
 * const result = await recognizeCaptcha(page, captchaElement);
 */
export async function recognizeCaptcha(page: Page, captchaElement: Locator): Promise<string> {
  // 获取验证码图片的 src 属性
  const captchaSrc = await captchaElement.getAttribute('src');

  if (!captchaSrc) {
    throw new Error('无法获取验证码图像');
  }

  // 处理 base64 数据
  const base64Data = captchaSrc.split(',')[1]; // 直接获取逗号后的base64数据

  return new Promise((resolve, reject) => {
    // 设置超时时间（毫秒）
    const TIMEOUT = 10000;
    let timeoutId: NodeJS.Timeout;

    // 调用 Python 脚本进行验证码识别
    const pythonScriptPath = path.resolve(__dirname, 'captcha_recognizer.py');
    console.log('Python脚本路径:', pythonScriptPath);
    console.log('Base64数据长度:', base64Data.length);

    const pythonProcess = spawn('python', [pythonScriptPath, base64Data], {
      env: {
        ...process.env,
        PYTHONIOENCODING: 'utf-8'
      },
      shell: true
    });

    let output = '';
    let errorOutput = '';

    // 处理Python脚本的标准输出
    pythonProcess.stdout.on('data', (data) => {
      const str = data.toString('utf8').trim();
      console.log('Python输出:', str);
      output += str;
    });

    // 处理Python脚本的错误输出
    pythonProcess.stderr.on('data', (data) => {
      const str = data.toString('utf8').trim();
      console.log('Python调试信息:', str);
      errorOutput += str;
    });

    // 处理Python脚本执行完成
    pythonProcess.on('close', (code) => {
      clearTimeout(timeoutId);
      if (code !== 0) {
        console.error('Python脚本执行失败，错误信息:', errorOutput);
        reject(new Error('验证码识别失败'));
        return;
      }

      if (!output) {
        console.error('Python脚本没有输出结果');
        reject(new Error('验证码识别失败'));
        return;
      }

      resolve(output);
    });

    // 处理Python脚本执行错误
    pythonProcess.on('error', (err) => {
      clearTimeout(timeoutId);
      console.error('Python脚本执行错误:', err);
      reject(new Error('验证码识别失败'));
    });

    // 设置超时处理
    timeoutId = setTimeout(() => {
      pythonProcess.kill();
      reject(new Error('验证码识别超时'));
    }, TIMEOUT);
  });
} 