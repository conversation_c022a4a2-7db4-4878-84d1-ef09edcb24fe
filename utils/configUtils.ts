import * as fs from 'fs';
import * as path from 'path';
import { DEFAULT_ENV } from '../config';

/**
 * 配置管理工具类
 * 提供配置文件读写和常量管理相关的功能
 */

/**
 * 更新或添加config.ts中的常量
 * 支持动态更新配置文件中的常量值
 * 
 * @param key 常量名称
 * @param value 常量值
 * @param env 环境名称（可选，不传则自动获取当前环境）
 * @param configPath config.ts文件路径，默认为项目根目录下的config.ts
 * @returns boolean 是否更新成功
 * 
 * @example
 * // 自动更新当前环境的BUSINESS配置
 * setConstant('WAREHOUSE', '上海仓库');
 * 
 * @description
 * 该函数会：
 * 1. 读取配置文件内容
 * 2. 根据env参数或当前环境更新BUSINESS配置
 * 3. 检查常量是否已存在
 * 4. 如果存在则更新，不存在则添加
 * 5. 保持文件格式和结构
 * 6. 写入更新后的内容
 * 
 * @throws Error 当文件读写失败时抛出错误
 */
export function setConstant(key: string, value: any, env?: 'FAT' | 'UAT' | 'PROD', configPath: string = path.resolve(__dirname, '../config.ts')): boolean {
  try {
    // 读取config.ts文件内容
    let content = fs.readFileSync(configPath, 'utf8');
    
    // 如果没有指定env，则尝试获取当前环境，如果都没有则使用默认环境
    const currentEnv = env || process.env.ENVIRONMENT as 'FAT' | 'UAT' | 'PROD' || DEFAULT_ENV;
    
    // 更新指定环境的BUSINESS配置
    const envRegex = new RegExp(`(export\\s+const\\s+BUSINESS\\s*=\\s*{[^}]*${currentEnv}:\\s*{[^}]*${key}:\\s*['"])[^'"]*(['"])`, 's');
    if (envRegex.test(content)) {
      content = content.replace(envRegex, `$1${value}$2`);
    } else {
      // 如果不存在，在对应环境的配置中添加
      const envConfigRegex = new RegExp(`(export\\s+const\\s+BUSINESS\\s*=\\s*{[^}]*${currentEnv}:\\s*{[^}]*)(})`, 's');
      if (envConfigRegex.test(content)) {
        content = content.replace(envConfigRegex, `$1  ${key}: '${value}',\n$2`);
      } else {
        // 如果环境配置不存在，创建环境配置
        const businessRegex = /(export\s+const\s+BUSINESS\s*=\s*{)([^}]*)(})/s;
        if (businessRegex.test(content)) {
          content = content.replace(businessRegex, `$1\n  ${currentEnv}: {\n    ${key}: '${value}'\n  },\n$2$3`);
        } else {
          throw new Error(`BUSINESS配置不存在`);
        }
      }
    }
    
    // 写入更新后的内容
    fs.writeFileSync(configPath, content, 'utf8');
    console.log(`成功更新环境 ${currentEnv} 的常量: ${key}`);
    return true;
  } catch (error) {
    console.error(`更新常量失败: ${key}`, error);
    return false;
  }
} 