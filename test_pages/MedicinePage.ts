import { Page } from '@playwright/test';
import { getCurrentConfig } from '../config';
import { MedicineFormParams, MedicineSearchParams } from '../test_datas/MedicineData';

const config = getCurrentConfig();

/**
 * 药品管理页面对象类
 */
export class MedicinePage {
  /**
   * 构造函数
   * @param page Playwright页面对象
   */
  constructor(private page: Page) {}

  // 打开页面
  async open() {
    await this.page.goto(config.URLS.TCMSP_URL + 'medicineManagement');
  }

  // 新增药品
  async add(data: MedicineFormParams) {
    await this.open();
    await this.page.getByRole('button', { name: '新增药品' }).click();
    await this.fillMedicineForm(data);
    await this.confirm();
  }

  // 确定按钮
  async confirm() {
    await this.page.getByRole('button', { name: '保 存' }).click();
  }

  // 取消按钮
  async cancel() {
    await this.page.getByRole('button', { name: '取 消' }).click();
  }

  // 编辑药品
  async edit(params: MedicineSearchParams = {}, data: MedicineFormParams) {
    await this.search(params);
    await this.page.getByText('编辑').first().click();
    await this.fillMedicineForm(data);
    await this.confirm();
  }

  // 清空搜索条件
  async clearSearch() {
    await this.page.getByRole('button', { name: '清空' }).click();
  }

  // 搜索药品
  async search(params: MedicineSearchParams = {}) {
    await this.open();

    // 清空搜索条件
    await this.clearSearch();

    // 填写搜索条件
    if (params.medicineNo) {
      // 使用一种定位方式，避免重复输入
      await this.page.getByPlaceholder('药品编号').fill(params.medicineNo);
    }

    if (params.medicineName) {
      await this.page.getByPlaceholder('药品名称').fill(params.medicineName);
    }

    if (params.createDateRange) {
      // 点击日期范围选择器
      await this.page.getByRole('textbox', { name: '开始日期' }).click();

      // 填写开始日期和结束日期
      await this.page.getByPlaceholder('开始日期').last().type(params.createDateRange.start);
      await this.page.getByPlaceholder('结束日期').last().type(params.createDateRange.end);
      // 点击查询按钮
      await this.page.getByRole('button', { name: '查询' }).click();
    }

    // 点击查询按钮
    await this.page.getByRole('button', { name: '查询' }).click();
  }

  // 填写药品表单
  async fillMedicineForm(data: MedicineFormParams) {
    try {
      // 填写基本信息
      if (data.name) {
        // 使用文本内容定位标签，然后找到相邻的输入框
        await this.page.locator('.search-body-col:has-text("药品名称") input').fill(data.name);
      }

      if (data.type) {
        // 点击药品类型选择框
        await this.page.locator('.search-body-col:has-text("药品类型") .ant-cascader').click();
        // 等待下拉菜单出现
        await this.page.waitForSelector('#cdk-overlay-0');
        // 选择药品类型 - 使用更精确的定位方式，限制在级联菜单内查找
        await this.page.getByRole('listitem', { name: '药品' }).click();
        await this.page.getByRole('listitem', { name: data.type }).click();
      }

      if (data.shortName) {
        await this.page.locator('.search-body-col:has-text("助记简称") input').fill(data.shortName);
      }

      if (data.unit) {
        // 点击包装单位选择框
        await this.page.locator('.search-body-col:has-text("包装单位") .below').click();
        // 选择包装单位 - 使用更精确的定位方式，限制在下拉菜单内查找
        await this.page.getByRole('listitem').filter({ hasText: data.unit }).first().click();


      }

      if (data.specification) {
        await this.page.locator('.search-body-col:has-text("规格") input').fill(data.specification);
      }

      if (data.dosageForm) {
        await this.page.locator('.search-body-col:has-text("剂型") input').fill(data.dosageForm);
      }

      if (data.manufacturer) {
        await this.page.locator('.search-body-col:has-text("生产厂家") input').fill(data.manufacturer);
      }

      if (data.origin) {
        await this.page.locator('.search-body-col:has-text("产地") input').fill(data.origin);
      }

      if (data.retailPrice) {
        await this.page.locator('.search-body-col:has-text("零售价") input').fill(data.retailPrice);
      }

      if (data.costPrice) {
        await this.page.locator('.search-body-col:has-text("成本价") input').fill(data.costPrice);
      }

      // 填写库存设置
      if (data.expiryWarningDays) {
        await this.page.locator('.search-body-col:has-text("过期预警时间") input').fill(data.expiryWarningDays);
      }

      if (data.stockWarningAmount) {
        await this.page.locator('.search-body-col:has-text("库存预警数量") input').fill(data.stockWarningAmount);
      }
    } catch (error) {
      console.error('填写表单失败:', error);
      // 添加截图帮助调试
      await this.page.screenshot({ path: `form-error-${Date.now()}.png` });
      throw error;
    }
  }

  // 查看库存详情
  async stockDetail(params: MedicineSearchParams = {}) {
    await this.search(params);
    await this.page.getByText('库存详情').first().click();

    // 等待库存详情页面加载
    await this.page.waitForSelector('.stock-detail-container');
  }

  // 下架药品
  async unshelve(params: MedicineSearchParams = {}) {
    await this.search(params);
    await this.page.getByText('下架').first().click();
    // 等待确认对话框
    await this.page.getByText('是否要下架?').waitFor();
    // 点击确认按钮
    await this.page.getByRole('button', { name: '确定' }).click();
  }

  // 删除药品
  async delete(params: MedicineSearchParams = {}) {
    await this.search(params);
    await this.page.getByText('删除').first().click();
    // 等待确认对话框
    await this.page.getByText('是否要删除？').waitFor();
    // 点击确认按钮
    await this.page.getByRole('button', { name: '确定' }).click();
  }

  /**
   * 根据列名获取列索引
   * @param columnName 列名
   * @returns 列索引（从1开始）
   */
  async getColumnIndexByName(columnName: string): Promise<number> {
    // 等待表格头部加载完成
    await this.page.waitForSelector('.datatable-header-cell-label', { state: 'visible', timeout: 10000 });

    // 等待特定列名出现
    try {
      await this.page.waitForFunction(
        (colName) => {
          const headers = document.querySelectorAll('.datatable-header-cell-label');
          return Array.from(headers).some(header => header.textContent?.trim() === colName);
        },
        columnName,
        { timeout: 10000 }
      );
    } catch (error) {
      console.error(`等待列名 "${columnName}" 出现超时`);
      // 继续执行，让下面的代码尝试查找列
    }

    const headers = this.page.locator('.datatable-header-cell-label');
    const count = await headers.count();

    if (count === 0) {
      throw new Error('表格头部未加载或未找到任何列');
    }

    for (let i = 0; i < count; i++) {
      const text = await headers.nth(i).textContent();
      if (text?.trim() === columnName) {
        return i + 1; // 返回1-based索引
      }
    }

    // 如果没有找到匹配的列名，记录所有可用的列名以便调试
    const availableColumns: string[] = [];
    for (let i = 0; i < count; i++) {
      const text = await headers.nth(i).textContent();
      if (text) {
        availableColumns.push(text.trim());
      }
    }

    throw new Error(`未找到列名为 "${columnName}" 的列。可用的列名有: ${availableColumns.join(', ')}`);
  }

  /**
   * 通用方法：获取指定行和列的单元格值
   * @param columnName 列名，如"药品名称"、"药品编号"等
   * @param rowIndex 行索引，从0开始，默认为0（第一行）
   * @returns 单元格的文本内容
   */
  async getCellValue(columnName: string, rowIndex: number = 0): Promise<string> {
    // 等待表格体加载完成
    await this.page.waitForSelector('.datatable-row-center.datatable-row-group', { state: 'visible', timeout: 10000 });

    // 获取列索引
    const columnIndex = await this.getColumnIndexByName(columnName);

    // 获取所有行
    const rows = this.page.locator('.datatable-row-center.datatable-row-group');
    const rowCount = await rows.count();

    if (rowCount === 0) {
      throw new Error('表格数据未加载或没有数据行');
    }

    if (rowIndex >= rowCount) {
      throw new Error(`行索引超出范围：请求的行索引为 ${rowIndex}，但总行数为 ${rowCount}`);
    }

    // 获取指定行
    const row = rows.nth(rowIndex);

    // 等待该行中的单元格加载完成
    await this.page.waitForSelector(`.datatable-row-center.datatable-row-group >> nth=${rowIndex} >> datatable-body-cell`,
      { state: 'visible', timeout: 5000 });

    // 获取单元格
    const cell = row.locator(`datatable-body-cell:nth-child(${columnIndex}) .datatable-body-cell-label`);

    try {
      // 等待单元格内容加载
      await cell.waitFor({ state: 'visible', timeout: 5000 });

      // 根据列名判断是否需要特殊处理
      if (['药品名称', '规格', '生产厂家'].includes(columnName)) {
        // 这些列的内容包装在 .cursor-pointer.width-content 元素中
        const content = cell.locator('.cursor-pointer.width-content');
        await content.waitFor({ state: 'visible', timeout: 3000 }).catch(() => {
          console.warn(`未找到 ${columnName} 列的 .cursor-pointer.width-content 元素，将尝试直接获取单元格内容`);
        });
        return (await content.textContent() || await cell.textContent() || '').trim();
      } else {
        // 其他列直接获取文本内容
        return (await cell.textContent() || '').trim();
      }
    } catch (error) {
      console.error(`获取 ${columnName} 列的值时出错:`, error);
      // 尝试直接获取单元格的innerHTML作为备选方案
      const innerHTML = await cell.evaluate(node => node.innerHTML);
      return innerHTML.replace(/<[^>]*>/g, '').trim();
    }
  }

  // 实现locator方法
  locator(selector: string) {
    return this.page.locator(selector);
  }

  // 实现getByRole方法
  getByRole(role: 'alert' | 'alertdialog' | 'application' | 'article' | 'banner' | 'blockquote' | 'button' | 'caption' | 'cell' | 'checkbox' | 'code' | 'columnheader' | 'combobox' | 'complementary' | 'contentinfo' | 'definition' | 'deletion' | 'dialog' | 'directory' | 'document' | 'emphasis' | 'feed' | 'figure' | 'form' | 'generic' | 'grid' | 'gridcell' | 'group' | 'heading' | 'img' | 'insertion' | 'link' | 'list' | 'listbox' | 'listitem' | 'log' | 'main' | 'marquee' | 'math' | 'meter' | 'menu' | 'menubar' | 'menuitem' | 'menuitemcheckbox' | 'menuitemradio' | 'navigation' | 'none' | 'note' | 'option' | 'paragraph' | 'presentation' | 'progressbar' | 'radio' | 'radiogroup' | 'region' | 'row' | 'rowgroup' | 'rowheader' | 'scrollbar' | 'search' | 'searchbox' | 'separator' | 'slider' | 'spinbutton' | 'status' | 'strong' | 'subscript' | 'superscript' | 'switch' | 'tab' | 'table' | 'tablist' | 'tabpanel' | 'term' | 'textbox' | 'time' | 'timer' | 'toolbar' | 'tooltip' | 'tree' | 'treegrid' | 'treeitem', options?: { name?: string | RegExp, exact?: boolean }) {
    return this.page.getByRole(role, options);
  }

  // 实现getByText方法
  getByText(text: string | RegExp, options?: { exact?: boolean }) {
    return this.page.getByText(text, options);
  }

  // 实现getByPlaceholder方法
  getByPlaceholder(text: string | RegExp, options?: { exact?: boolean }) {
    return this.page.getByPlaceholder(text, options);
  }

  // 实现getByLabel方法
  getByLabel(text: string | RegExp, options?: { exact?: boolean }) {
    return this.page.getByLabel(text, options);
  }
}




