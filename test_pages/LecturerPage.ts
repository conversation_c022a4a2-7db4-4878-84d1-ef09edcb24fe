import { Page } from '@playwright/test';
import { getCurrentConfig } from '../config';
import { LecturerFormParams, LecturerSearchParams } from '../test_datas/LecturerData';

const config = getCurrentConfig();

/**
 * 讲师管理页面对象类
 */
export class LecturerPage {
  /**
   * 构造函数
   * @param page Playwright页面对象
   */
  constructor(private page: Page) {}

  /**
   * 打开讲师管理页面
   */
  async open() {
    // TODO: 替换为实际的讲师管理页面路径
    await this.page.goto(config.URLS.TCMSP_URL + 'lecturerManagement');
  }

  /**
   * 新增讲师
   * @param data 讲师表单参数
   */
  async add(data: LecturerFormParams) {
    await this.open();
    // 点击新增讲师按钮
    await this.page.locator('button:has-text("新增讲师")').click();
    // TODO: 等待新增讲师对话框出现，需要根据实际页面结构调整
    // await this.page.waitForSelector('app-create-lecturer .modal-title:has-text("新增讲师")');
    await this.fillLecturerForm(data);
    await this.confirm();
  }

  /**
   * 确定按钮
   */
  async confirm() {
    await this.page.getByRole('button', { name: '保 存' }).click();
  }

  /**
   * 取消按钮
   */
  async cancel() {
    await this.page.getByRole('button', { name: '取 消' }).click();
  }

  /**
   * 关闭按钮
   */
  async close() {
    await this.page.getByRole('button', { name: '关 闭' }).click();
  }

  /**
   * 编辑讲师
   * @param searchParams 搜索参数，用于定位要编辑的讲师
   * @param formParams 表单参数，用于填写编辑表单
   */
  async edit(searchParams: LecturerSearchParams, formParams: LecturerFormParams) {
    await this.search(searchParams);
    await this.page.locator('a:has-text("编辑")').first().click();
    await this.fillLecturerForm(formParams);
    await this.confirm();
  }

  /**
   * 删除讲师
   * @param searchParams 搜索参数，用于定位要删除的讲师
   */
  async delete(searchParams: LecturerSearchParams) {
    await this.search(searchParams);
    await this.page.getByText('删除').first().click();
    // 等待确认对话框
    await this.page.getByText('确定要删除该讲师？').waitFor();
    // 确认删除
    await this.page.getByRole('button', { name: '确定' }).click();
  }

  /**
   * 查看讲师详情
   * @param searchParams 搜索参数，用于定位要查看的讲师
   */
  async detail(searchParams: LecturerSearchParams) {
    await this.search(searchParams);
    await this.page.getByText('详情').first().click();
    // 等待详情页面加载
    await this.page.waitForSelector('.modal-dialog');
  }

  /**
   * 讲师配置
   * @param searchParams 搜索参数，用于定位要配置的讲师
   */
  async config(searchParams: LecturerSearchParams) {
    await this.search(searchParams);
    await this.page.locator('a:has-text("配置")').first().click();
    // TODO: 等待配置页面加载，需要根据实际页面结构调整
  }

  /**
   * 关联账号
   * @param searchParams 搜索参数，用于定位要关联账号的讲师
   */
  async relateAccount(searchParams: LecturerSearchParams) {
    await this.search(searchParams);
    await this.page.locator('a:has-text("关联账号")').first().click();
    // TODO: 等待关联账号页面加载，需要根据实际页面结构调整
  }

  /**
   * 搜索讲师
   * @param params 搜索参数
   */
  async search(params: LecturerSearchParams = {}) {
    await this.open();

    // 清空搜索条件
    await this.clearSearch();

    // 填写搜索条件
    if (params.lecturerId) {
      await this.page.getByPlaceholder('讲师ID').fill(params.lecturerId);
    }

    if (params.lecturerName) {
      await this.page.getByPlaceholder('讲师姓名').fill(params.lecturerName);
    }

    if (params.phone) {
      await this.page.getByPlaceholder('手机号').fill(params.phone);
    }

    if (params.lecturerAccount) {
      await this.page.getByPlaceholder('讲师账号').fill(params.lecturerAccount);
    }

    // 处理下拉选择框
    if (params.region) {
      await this.selectRegion(params.region);
    }

    if (params.organization) {
      await this.selectOrganization(params.organization);
    }

    if (params.hospital) {
      await this.selectHospital(params.hospital);
    }

    if (params.department) {
      await this.selectDepartment(params.department);
    }

    if (params.title) {
      await this.selectTitle(params.title);
    }

    if (params.lecturerCategory) {
      await this.selectLecturerCategory(params.lecturerCategory);
    }

    if (params.hasAccount) {
      await this.selectHasAccount(params.hasAccount);
    }

    // TODO: 处理创建时间范围选择
    if (params.createTimeStart && params.createTimeEnd) {
      await this.selectCreateTimeRange(params.createTimeStart, params.createTimeEnd);
    }

    // 点击查询按钮（底部的查询按钮）
    await this.page.locator('.btn-search:has-text("查询")').click();
  }

  /**
   * 清空搜索条件
   */
  async clearSearch() {
    // 点击顶部的清空按钮
    await this.page.locator('button:has-text("清空")').first().click();
  }

  /**
   * 选择所在地区
   * @param region 地区名称
   */
  async selectRegion(region: string) {
    // 点击地区下拉框
    await this.page.locator('app-region-select .m-dropdown__toggle').click();
    // 等待下拉选项出现
    await this.page.waitForSelector('.m-dropdown__content .box');
    // 选择对应地区
    await this.page.locator(`.m-dropdown__content .box p:has-text("${region}")`).click();
  }

  /**
   * 选择所属机构
   * @param organization 机构名称
   */
  async selectOrganization(organization: string) {
    // 点击所属机构下拉框
    await this.page.locator('app-belong-mechanism ng-select .below').first().click();
    // 等待下拉选项出现并选择
    await this.page.waitForSelector('ng-option');
    await this.page.locator(`ng-option:has-text("${organization}")`).click();
  }

  /**
   * 选择坐诊医院
   * @param hospital 医院名称
   */
  async selectHospital(hospital: string) {
    // 点击坐诊医院下拉框（第二个app-belong-mechanism）
    await this.page.locator('app-belong-mechanism ng-select .below').nth(1).click();
    // 等待下拉选项出现并选择
    await this.page.waitForSelector('ng-option');
    await this.page.locator(`ng-option:has-text("${hospital}")`).click();
  }

  /**
   * 选择所在科室
   * @param department 科室名称
   */
  async selectDepartment(department: string) {
    // 点击所在科室下拉框
    await this.page.locator('label:has-text("所在科室") + div ng-select .below').click();
    // 等待下拉选项出现并选择
    await this.page.waitForSelector('ng-option');
    await this.page.locator(`ng-option:has-text("${department}")`).click();
  }

  /**
   * 选择职称
   * @param title 职称名称
   */
  async selectTitle(title: string) {
    // 点击职称下拉框
    await this.page.locator('label:has-text("职称") + div ng-select .below').click();
    // 等待下拉选项出现并选择
    await this.page.waitForSelector('ng-option');
    await this.page.locator(`ng-option:has-text("${title}")`).click();
  }

  /**
   * 选择讲师分类
   * @param category 讲师分类名称
   */
  async selectLecturerCategory(category: string) {
    // 点击讲师分类下拉框
    await this.page.locator('label:has-text("讲师分类") + div ng-select .below').click();
    // 等待下拉选项出现并选择
    await this.page.waitForSelector('ng-option');
    await this.page.locator(`ng-option:has-text("${category}")`).click();
  }

  /**
   * 选择有无账号
   * @param hasAccount 有无账号状态
   */
  async selectHasAccount(hasAccount: string) {
    // 点击有无账号下拉框
    await this.page.locator('label:has-text("有无账号") + div ng-select .below').click();
    // 等待下拉选项出现并选择
    await this.page.waitForSelector('ng-option');
    await this.page.locator(`ng-option:has-text("${hasAccount}")`).click();
  }

  /**
   * 选择创建时间范围
   * @param startDate 开始日期
   * @param endDate 结束日期
   */
  async selectCreateTimeRange(startDate: string, endDate: string) {
    // 点击日期范围选择器
    await this.page.locator('app-custom-range-picker .ant-calendar-picker').click();
    // 等待日期选择器打开
    await this.page.waitForSelector('.ant-calendar-panel');
    
    // 输入开始日期
    await this.page.locator('.ant-calendar-range-picker-input').first().fill(startDate);
    // 输入结束日期
    await this.page.locator('.ant-calendar-range-picker-input').nth(1).fill(endDate);
    
    // 点击确定或点击其他地方关闭日期选择器
    await this.page.keyboard.press('Escape');
  }

  /**
   * 填写讲师表单
   * @param data 讲师表单参数
   */
  async fillLecturerForm(data: LecturerFormParams) {
    try {
      // TODO: 根据实际新增/编辑表单结构填写各个字段
      // 填写基本信息
      if (data.lecturerName) {
        await this.page.locator('input[placeholder="讲师姓名"]').fill(data.lecturerName);
      }

      if (data.phone) {
        await this.page.locator('input[placeholder="手机号"]').fill(data.phone);
      }

      // TODO: 其他字段的填写逻辑

    } catch (error) {
      console.error('填写表单失败:', error);
      // 添加截图帮助调试
      await this.page.screenshot({ path: `lecturer-form-error-${Date.now()}.png` });
      throw error;
    }
  }

  /**
   * 获取表格单元格值
   * @param columnName 列名
   * @param rowIndex 行索引，默认为0（第一行）
   * @returns 单元格值
   */
  async getCellValue(columnName: string, rowIndex: number = 0): Promise<string> {
    // 获取列索引
    const headers = await this.page.locator('.datatable-header-cell-label').allTextContents();
    const columnIndex = headers.findIndex(header => header === columnName);

    if (columnIndex === -1) {
      throw new Error(`找不到列 "${columnName}"`);
    }

    // 获取单元格值
    const cell = this.page.locator('.datatable-row-center').nth(rowIndex).locator('.datatable-body-cell').nth(columnIndex);
    return await cell.textContent() || '';
  }

  // 实现locator方法
  locator(selector: string) {
    return this.page.locator(selector);
  }

  // 实现getByRole方法
  getByRole(role: 'alert' | 'alertdialog' | 'application' | 'article' | 'banner' | 'blockquote' | 'button' | 'caption' | 'cell' | 'checkbox' | 'code' | 'columnheader' | 'combobox' | 'complementary' | 'contentinfo' | 'definition' | 'deletion' | 'dialog' | 'directory' | 'document' | 'emphasis' | 'feed' | 'figure' | 'form' | 'generic' | 'grid' | 'gridcell' | 'group' | 'heading' | 'img' | 'insertion' | 'link' | 'list' | 'listbox' | 'listitem' | 'log' | 'main' | 'marquee' | 'math' | 'meter' | 'menu' | 'menubar' | 'menuitem' | 'menuitemcheckbox' | 'menuitemradio' | 'navigation' | 'none' | 'note' | 'option' | 'paragraph' | 'presentation' | 'progressbar' | 'radio' | 'radiogroup' | 'region' | 'row' | 'rowgroup' | 'rowheader' | 'scrollbar' | 'search' | 'searchbox' | 'separator' | 'slider' | 'spinbutton' | 'status' | 'strong' | 'subscript' | 'superscript' | 'switch' | 'tab' | 'table' | 'tablist' | 'tabpanel' | 'term' | 'textbox' | 'time' | 'timer' | 'toolbar' | 'tooltip' | 'tree' | 'treegrid' | 'treeitem', options?: { name?: string | RegExp, exact?: boolean }) {
    return this.page.getByRole(role, options);
  }

  // 实现getByText方法
  getByText(text: string | RegExp, options?: { exact?: boolean }) {
    return this.page.getByText(text, options);
  }

  // 实现getByPlaceholder方法
  getByPlaceholder(text: string | RegExp, options?: { exact?: boolean }) {
    return this.page.getByPlaceholder(text, options);
  }

  // 实现getByLabel方法
  getByLabel(text: string | RegExp, options?: { exact?: boolean }) {
    return this.page.getByLabel(text, options);
  }
} 