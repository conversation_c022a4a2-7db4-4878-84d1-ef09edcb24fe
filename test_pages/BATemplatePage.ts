import { Page } from '@playwright/test';
import { getCurrentConfig } from '../config';
import { FormParams, SearchParams } from '../test_datas/BATemplateData';

const config = getCurrentConfig();

export class BATemplatePage {
  constructor(private page: Page) { }

  // 打开页面
  async open() {
    await this.page.goto(config.URLS.TCMSP_URL + 'templateManagement');
    await this.page.getByText('病案模板', { exact: true }).click();
  }

  // 新增模板
  async add(data: FormParams) {
    await this.open();
    await this.page.click('button:has-text("新增模板")');
    await this.fillForm(data);
    await this.confirm();
  }

  // 确定按钮
  async confirm() {
    await this.page.click('button:has-text("确 定")');
  }

  // 取消按钮
  async cancel() {
    await this.page.click('button:has-text("取 消")');
  }

  // 关闭按钮
  async close() {
    await this.page.click('button:has-text("关 闭")');
  }

  // 编辑按钮
  async edit(params: SearchParams = {}, data: FormParams) {
    await this.search(params);
    await this.page.locator('.datatable-body-row:first-child .operation-a_hover:has-text("编辑")').click();
    await this.fillForm(data);
    await this.confirm();
  }

  // 详情按钮
  async detail(params: SearchParams = {}) {
    await this.search(params);
    await this.page.locator('.datatable-body-row:first-child .operation-a_hover:has-text("详情")').click();
  }

  // 清空按钮
  async clearSearch() {
    await this.page.click('button.btn-reset');
  }

  // 列表查询
  async search(params: SearchParams = {}) {
    await this.open();

    // 判断并点击展开按钮
    const expandButton = this.page.locator('label:has-text("展开")');
    const isVisible = await expandButton.isVisible();
    if (isVisible) {
      await expandButton.click();
    }

    // 填写查询条件
    if (params.templateNo) {
      await this.page.locator('input[placeholder="模板编号"]').fill(params.templateNo);
    }
    if (params.name) {
      await this.page.locator('input[placeholder="模板名称"]').fill(params.name);
    }
    if (params.type) {
      await this.page.locator('label:has-text("模板分类") + div').click();
      await this.page.locator('label:has-text("模板分类") + div input').fill(params.type);
      await this.page.locator(`.options li span:has-text("${params.type}")`).click();
    }
    if (params.diagnosis) {
      await this.page.locator('input[placeholder="诊断"]').fill(params.diagnosis);
    }
    if (params.description) {
      await this.page.locator('input[placeholder="说明"]').fill(params.description);
    }
    if (params.creator) {
      await this.page.locator('input[placeholder="创建人"]').fill(params.creator);
    }
    if (params.createTimeRange) {
      await this.page.locator('label:has-text("创建时间") + div').click();
      // 等待日期面板显示
      await this.page.waitForSelector('.ant-calendar-date-panel');
      // 使用更精确的选择器定位日期输入框
      const startDateInput = this.page.locator('.ant-calendar-range-left .ant-calendar-input-wrap input[placeholder="开始日期"]');
      const endDateInput = this.page.locator('.ant-calendar-range-right .ant-calendar-input-wrap input[placeholder="结束日期"]');

      // 点击并输入开始日期
      await startDateInput.click();
      for (const char of params.createTimeRange.start) {
        await this.page.keyboard.press(char);
      }

      // 点击并输入结束日期
      await endDateInput.click();
      for (const char of params.createTimeRange.end) {
        await this.page.keyboard.press(char);
      }
    }
    // 点击查询按钮
    await this.page.click('button.btn-search');
  }

  // 填写模板表单
  async fillForm(data: FormParams) {
    try {
      // 填写模板信息
      await this.page.locator('.prescription-item .medication-title:has-text("模板名称") + textarea').fill(data.name);
      await this.page.locator('.m-radio:has-text("中医病案")').click();
      await this.page.locator('.prescription-item .medication-title:has-text("诊断") + textarea').fill(data.diagnosis);
      await this.page.locator('.prescription-item .medication-title:has-text("说明") + textarea').fill(data.description);

      // 填写模板内容
      if (data.chiefComplaint) {
        await this.page.locator('.disease-content .medication-title:has-text("病情主诉") + .flex_1 textarea').fill(data.chiefComplaint);
      }
      if (data.physicalExam) {
        await this.page.locator('.disease-content .medication-title:has-text("体格检查") + .flex_1 textarea').fill(data.physicalExam);
      }
      if (data.presentIllness) {
        await this.page.locator('.disease-content .medication-title:has-text("现病史") + .flex_1 textarea').fill(data.presentIllness);
      }

      // 处理病史类型
      const historyTypes = [
        { label: "既往史", value: data.pastHistory },
        { label: "个人史", value: data.personalHistory },
        { label: "家族史", value: data.familyHistory },
        { label: "过敏史", value: data.allergicHistory }
      ];

      // 处理每种病史类型
      for (const { label, value } of historyTypes) {
        if (value) {
          // 检查复选框是否已选中
          const checkbox = this.page.locator(`.m-checkbox:has-text("${label}") input[type="checkbox"]`);
          const isChecked = await checkbox.isChecked();
          if (!isChecked) {
            await this.page.locator(`.m-checkbox:has-text("${label}")`).click();
          }
          // 填写对应的文本框
          await this.page.locator(`.disease-content .medication-title:has-text("${label}") + .flex_1 textarea`).fill(value);
        }
      }

      // 填写其他字段
      if (data.marriageHistory) {
        await this.page.locator('.disease-content .medication-title:has-text("婚育史") + .flex_1 textarea').fill(data.marriageHistory);
      }
      if (data.symptoms) {
        await this.page.locator('.disease-content .medication-title:has-text("望闻问切") + .flex_1 textarea').fill(data.symptoms);
      }
    } catch (error) {
      console.error('填写表单失败:', error);
      // 添加截图帮助调试
      await this.page.screenshot({ path: `form-error-${Date.now()}.png` });
      throw error; // 重新抛出错误，保持原有行为
    }
  }

  // 实现locator方法
  locator(selector: string) {
    return this.page.locator(selector);
  }

  // 实现getByRole方法
  getByRole(role: 'alert' | 'alertdialog' | 'application' | 'article' | 'banner' | 'blockquote' | 'button' | 'caption' | 'cell' | 'checkbox' | 'code' | 'columnheader' | 'combobox' | 'complementary' | 'contentinfo' | 'definition' | 'deletion' | 'dialog' | 'directory' | 'document' | 'emphasis' | 'feed' | 'figure' | 'form' | 'generic' | 'grid' | 'gridcell' | 'group' | 'heading' | 'img' | 'insertion' | 'link' | 'list' | 'listbox' | 'listitem' | 'log' | 'main' | 'marquee' | 'math' | 'meter' | 'menu' | 'menubar' | 'menuitem' | 'menuitemcheckbox' | 'menuitemradio' | 'navigation' | 'none' | 'note' | 'option' | 'paragraph' | 'presentation' | 'progressbar' | 'radio' | 'radiogroup' | 'region' | 'row' | 'rowgroup' | 'rowheader' | 'scrollbar' | 'search' | 'searchbox' | 'separator' | 'slider' | 'spinbutton' | 'status' | 'strong' | 'subscript' | 'superscript' | 'switch' | 'tab' | 'table' | 'tablist' | 'tabpanel' | 'term' | 'textbox' | 'time' | 'timer' | 'toolbar' | 'tooltip' | 'tree' | 'treegrid' | 'treeitem', options?: { name?: string | RegExp, exact?: boolean }) {
    return this.page.getByRole(role, options);
  }

  // 实现getByText方法
  getByText(text: string | RegExp, options?: { exact?: boolean }) {
    return this.page.getByText(text, options);
  }

  // 实现getByPlaceholder方法
  getByPlaceholder(text: string | RegExp, options?: { exact?: boolean }) {
    return this.page.getByPlaceholder(text, options);
  }

  // 实现getByLabel方法
  getByLabel(text: string | RegExp, options?: { exact?: boolean }) {
    return this.page.getByLabel(text, options);
  }

  // 右下角提示     await expect(page.getByRole('alertdialog')).toContainText('保存失败。只有医师角色的账号才有此操作权限');
}