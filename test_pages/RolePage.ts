import { Page } from '@playwright/test';
import { getCurrentConfig } from '../config';
import { RoleFormParams, RoleSearchParams } from '../test_datas/RoleData';

const config = getCurrentConfig();

/**
 * 角色管理页面对象类
 */
export class RolePage {
  /**
   * 构造函数
   * @param page Playwright页面对象
   */
  constructor(private page: Page) {}

  /**
   * 打开角色管理页面
   */
  async open() {
    // 假设角色管理页面的URL路径是roleManagement
    await this.page.goto(config.URLS.TCMSP_URL + 'privilegeRole');
  }

  /**
   * 新增角色
   * @param data 角色表单参数
   */
  async add(data: RoleFormParams) {
    await this.open();
    // 点击新增按钮，根据HTML结构调整选择器
    await this.page.getByText('新 增').click();
    // 等待新增角色对话框出现
    await this.page.waitForSelector('app-create-role .modal-title:has-text("新增角色")');
    await this.fillRoleForm(data);
    await this.confirm();
  }

  /**
   * 确定按钮
   */
  async confirm() {
    await this.page.getByRole('button', { name: '保 存' }).click();
  }

  /**
   * 取消按钮
   */
  async cancel() {
    await this.page.getByRole('button', { name: '取 消' }).click();
  }

  /**
   * 关闭按钮
   */
  async close() {
    await this.page.getByRole('button', { name: '关 闭' }).click();
  }

  /**
   * 编辑角色
   * @param searchParams 搜索参数，用于定位要编辑的角色
   * @param formParams 表单参数，用于填写编辑表单
   */
  async edit(searchParams: RoleSearchParams, formParams: RoleFormParams) {
    await this.search(searchParams);
    await this.page.getByText('编辑').first().click();
    await this.fillRoleForm(formParams);
    await this.confirm();
  }

  /**
   * 删除角色
   * @param searchParams 搜索参数，用于定位要删除的角色
   */
  async delete(searchParams: RoleSearchParams) {
    await this.search(searchParams);
    await this.page.getByText('删除').first().click();
    // 等待确认对话框
    await this.page.getByText('确定要删除该角色？').waitFor();
    // 确认删除
    await this.page.getByRole('button', { name: '确定' }).click();
  }

  /**
   * 查看角色详情
   * @param searchParams 搜索参数，用于定位要查看的角色
   */
  async detail(searchParams: RoleSearchParams) {
    await this.search(searchParams);
    await this.page.getByText('详情').first().click();
    // 等待详情页面加载
    await this.page.waitForSelector('.modal-dialog');
  }

  /**
   * 角色授权
   * @param searchParams 搜索参数，用于定位要授权的角色
   */
  async authorization(searchParams: RoleSearchParams) {
    await this.search(searchParams);
    await this.page.getByText('授权').first().click();
    // 等待授权页面加载
    await this.page.waitForSelector('app-role-menus');
    await this.page.locator('#allSelect').check();
    await this.page.getByRole('button', { name: '添加' }).click();
  }

  /**
   * 搜索角色
   * @param params 搜索参数
   */
  async search(params: RoleSearchParams = {}) {
    await this.open();

    // 清空搜索条件
    await this.clearSearch();

    // 填写搜索条件
    if (params.organization) {
      await this.page.getByPlaceholder('所属机构').fill(params.organization);
    }

    if (params.roleName) {
      await this.page.getByPlaceholder('角色名称').fill(params.roleName);
    }

    // 点击查询按钮
    await this.page.getByRole('button', { name: '查询' }).click();
  }

  /**
   * 清空搜索条件
   */
  async clearSearch() {
    await this.page.getByRole('button', { name: '清空' }).click();
  }

  /**
   * 填写角色表单
   * @param data 角色表单参数
   */
  async fillRoleForm(data: RoleFormParams) {
    try {
      // 填写基本信息
      if (data.roleName) {
        await this.page.locator('.search-body-col:has-text("角色名称") input').fill(data.roleName);
      }

      // 处理数据权限下拉选择
      if (data.dataPermission) {
        // 点击下拉框打开选项列表
        await this.page.locator('.search-body-col:has-text("数据权限") .select-drop-down').click();
        // 等待下拉选项出现
        await this.page.waitForSelector('.below');
        // 选择对应的选项
        await this.page.getByRole('listitem').filter({ hasText: data.dataPermission }).click();
      }

    } catch (error) {
      console.error('填写表单失败:', error);
      // 添加截图帮助调试
      await this.page.screenshot({ path: `form-error-${Date.now()}.png` });
      throw error;
    }
  }

  /**
   * 获取表格单元格值
   * @param columnName 列名
   * @param rowIndex 行索引，默认为0（第一行）
   * @returns 单元格值
   */
  async getCellValue(columnName: string, rowIndex: number = 0): Promise<string> {
    // 获取列索引
    const headers = await this.page.locator('.datatable-header-cell-label').allTextContents();
    const columnIndex = headers.findIndex(header => header === columnName);

    if (columnIndex === -1) {
      throw new Error(`找不到列 "${columnName}"`);
    }

    // 获取单元格值
    const cell = this.page.locator('.datatable-row-center').nth(rowIndex).locator('.datatable-body-cell').nth(columnIndex);
    return await cell.textContent() || '';
  }

  // 实现locator方法
  locator(selector: string) {
    return this.page.locator(selector);
  }

  // 实现getByRole方法
  getByRole(role: 'alert' | 'alertdialog' | 'application' | 'article' | 'banner' | 'blockquote' | 'button' | 'caption' | 'cell' | 'checkbox' | 'code' | 'columnheader' | 'combobox' | 'complementary' | 'contentinfo' | 'definition' | 'deletion' | 'dialog' | 'directory' | 'document' | 'emphasis' | 'feed' | 'figure' | 'form' | 'generic' | 'grid' | 'gridcell' | 'group' | 'heading' | 'img' | 'insertion' | 'link' | 'list' | 'listbox' | 'listitem' | 'log' | 'main' | 'marquee' | 'math' | 'meter' | 'menu' | 'menubar' | 'menuitem' | 'menuitemcheckbox' | 'menuitemradio' | 'navigation' | 'none' | 'note' | 'option' | 'paragraph' | 'presentation' | 'progressbar' | 'radio' | 'radiogroup' | 'region' | 'row' | 'rowgroup' | 'rowheader' | 'scrollbar' | 'search' | 'searchbox' | 'separator' | 'slider' | 'spinbutton' | 'status' | 'strong' | 'subscript' | 'superscript' | 'switch' | 'tab' | 'table' | 'tablist' | 'tabpanel' | 'term' | 'textbox' | 'time' | 'timer' | 'toolbar' | 'tooltip' | 'tree' | 'treegrid' | 'treeitem', options?: { name?: string | RegExp, exact?: boolean }) {
    return this.page.getByRole(role, options);
  }

  // 实现getByText方法
  getByText(text: string | RegExp, options?: { exact?: boolean }) {
    return this.page.getByText(text, options);
  }

  // 实现getByPlaceholder方法
  getByPlaceholder(text: string | RegExp, options?: { exact?: boolean }) {
    return this.page.getByPlaceholder(text, options);
  }

  // 实现getByLabel方法
  getByLabel(text: string | RegExp, options?: { exact?: boolean }) {
    return this.page.getByLabel(text, options);
  }
}
