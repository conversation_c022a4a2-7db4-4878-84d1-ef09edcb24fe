# UI测试计划规则 (UI Test Plan Rule)

## 1. 规则概述
当用户提供网页链接时，自动调用playwright-mcp工具分析页面元素，生成完整的UI测试计划文档。

## 2. 触发条件
- 用户提供HTTP/HTTPS链接
- 用户要求分析某个网页
- 用户要求生成测试计划

## 3. 自动化执行流程

### 3.1 页面导航与初始化
```
mcp_playwright_playwright_navigate
- url: 用户提供的链接
- headless: false (方便调试)
- width: 1920
- height: 1080
```

### 3.2 获取页面结构信息
```
mcp_playwright_playwright_get_visible_html
- cleanHtml: true
- removeScripts: true
- removeStyles: false
- maxLength: 20000
```

### 3.3 截取页面截图
```
mcp_playwright_playwright_screenshot
- name: "page_analysis"
- fullPage: true
- savePng: true
```

### 3.4 获取页面可见文本
```
mcp_playwright_playwright_get_visible_text
```

### 3.5 获取页面元素详细信息
```
mcp_playwright_playwright_evaluate
script: 获取所有表单元素的详细属性信息
```

## 4. 元素分析规则

### 4.1 元素类型识别
| 元素类型 | 识别方式 | 示例 |
|----------|----------|------|
| 输入框   | input[type="text/email/password/number"] | #username |
| 按钮     | button, input[type="submit/button"] | #login-btn |
| 下拉框   | select | #category |
| 单选框   | input[type="radio"] | name="gender" |
| 复选框   | input[type="checkbox"] | #agreement |
| 文本域   | textarea | #comments |
| 上传框   | input[type="file"] | #file-upload |

### 4.2 定位方式优先级
1. ID选择器 (#element-id)
2. Name属性 (name="element-name")
3. Class选择器 (.element-class)
4. 属性选择器 ([placeholder="text"])
5. CSS选择器组合

## 5. 测试计划文档模板

### 5.1 基本信息
- 页面标题
- 页面URL
- 测试目标
- 测试范围

### 5.2 元素清单
| 元素类型 | 功能描述 | 定位方式（ID/选择器） | 占位符/说明 |
|----------|----------|----------------------|-------------|
| 输入框   | 用户名输入 | #username | 用户名 |
| 按钮     | 登录按钮   | #login-btn | 登录 |

### 5.3 测试用例设计
#### 输入框类测试
- 正常输入测试
- 边界值测试
- 非法字符测试
- 必填项验证

#### 按钮类测试
- 点击功能测试
- 状态变化测试
- 权限控制测试

#### 表单提交测试
- 完整流程测试
- 错误处理测试
- 数据验证测试

### 5.4 Playwright自动化脚本示例
```javascript
// 页面导航
await page.goto('页面URL');

// 元素定位和操作示例
await page.fill('#username', '测试用户名');
await page.click('#login-btn');
await page.waitForSelector('.success-message');
```

## 6. 执行规则

### 6.1 自动执行条件
- 检测到用户输入包含HTTP/HTTPS链接
- 用户明确要求页面分析
- 用户要求生成测试计划

### 6.2 执行步骤
1. 自动导航到指定页面
2. 并行执行页面信息获取（HTML、截图、文本、元素）
3. 分析页面结构和元素类型
4. 生成完整的测试计划文档
5. 提供Playwright自动化脚本示例

### 6.3 输出格式
- 使用Markdown格式
- 包含表格形式的元素清单
- 提供代码块形式的脚本示例
- 包含测试用例分类和说明

## 7. 特殊处理规则

### 7.1 复杂页面处理
- 如果页面包含iframe，需要单独分析iframe内容
- 如果页面有动态加载内容，需要等待加载完成
- 如果页面需要登录，提示用户先登录

### 7.2 元素定位优化
- 优先使用稳定的ID选择器
- 避免使用易变的class选择器
- 提供多种备选定位方式

### 7.3 测试计划完整性
- 确保覆盖所有可交互元素
- 包含正向和负向测试用例
- 提供清晰的测试步骤和预期结果

## 8. 错误处理

### 8.1 页面访问失败
- 检查网络连接
- 验证URL有效性
- 提示用户检查页面权限

### 8.2 元素获取失败
- 重试机制
- 降级处理方案
- 手动补充元素信息

### 8.3 生成文档异常
- 提供基础模板
- 逐步完善内容
- 用户反馈优化

