import { test, expect } from '@playwright/test';
import { LecturerPage } from '../test_pages/LecturerPage';
import { getCurrentConfig } from '../config';
import { BrowserManager } from '../utils/utils';
import * as testData from '../test_datas/LecturerData';

const config = getCurrentConfig();

// 配置测试用例为串行执行，确保测试用例按顺序执行
test.describe.configure({ mode: 'serial' });

test.describe('讲师管理', () => {
  // 定义全局变量
  let lecturerPage: LecturerPage;
  let browserManager: BrowserManager;
  let lecturerId: string;

  test.beforeAll(async ({ browser }) => {
    // 初始化浏览器管理器
    browserManager = BrowserManager.getInstance();
    // 创建讲师管理页面实例，使用管理员权限
    lecturerPage = await browserManager.createPage(browser, config.STORAGE.ADMIN_STORAGE, LecturerPage);
  });

  test.describe('ZYWZ-讲师 讲师基本操作', () => {
    /**
     * 测试用例：空数据保存
     * 步骤：
     * 1. 打开讲师管理页面
     * 2. 点击新增讲师按钮
     * 3. 不填写任何数据
     * 4. 点击保存按钮
     * 5. 验证错误提示
     */
    test('空数据保存', async () => {
      // 执行新增讲师操作
      await lecturerPage.add(testData.emptyData);
      // 验证错误提示
      await expect(lecturerPage.getByRole('alertdialog')).toContainText('表单数据不完整，请检查');
      await lecturerPage.cancel();
    });

    /**
     * 测试用例：添加完整字段讲师
     * 步骤：
     * 1. 打开讲师管理页面
     * 2. 点击新增讲师按钮
     * 3. 填写所有字段
     * 4. 点击保存按钮
     * 5. 验证讲师是否创建成功
     */
    test('添加完整字段讲师', async () => {
      // 执行新增讲师操作
      await lecturerPage.add(testData.basicLecturerData);
      // 验证讲师是否创建成功
      await lecturerPage.search({ lecturerName: testData.basicLecturerData.lecturerName });
             await expect(lecturerPage.locator('datatable-body-row')).toBeVisible();
       await expect(lecturerPage.locator('datatable-body-row')).toContainText(testData.basicLecturerData.lecturerName || '');
    });

    /**
     * 测试用例：搜索功能测试
     * 步骤：
     * 1. 打开讲师管理页面
     * 2. 填写搜索条件
     * 3. 点击查询按钮
     * 4. 验证搜索结果
     */
    test('搜索功能', async () => {
      // 执行搜索操作
      await lecturerPage.search(testData.searchData);
      // 验证搜索结果
      await expect(lecturerPage.locator('datatable-body-row')).toBeVisible();
      if (testData.searchData.lecturerName) {
        await expect(lecturerPage.locator('datatable-body-row')).toContainText(testData.searchData.lecturerName);
      }
    });

    /**
     * 测试用例：清空搜索条件
     * 步骤：
     * 1. 打开讲师管理页面
     * 2. 填写搜索条件
     * 3. 点击清空按钮
     * 4. 验证搜索条件是否清空
     */
         test('清空搜索条件', async () => {
       // 先填写搜索条件
       await lecturerPage.search(testData.searchData);
       // 清空搜索条件
       await lecturerPage.clearSearch();
       // 验证输入框已清空
       await expect(lecturerPage.locator('input[placeholder="讲师ID"]')).toHaveValue('');
       await expect(lecturerPage.locator('input[placeholder="讲师姓名"]')).toHaveValue('');
       await expect(lecturerPage.locator('input[placeholder="手机号"]')).toHaveValue('');
     });

    /**
     * 测试用例：表格字段展示
     * 步骤：
     * 1. 打开讲师管理页面
     * 2. 验证表格表头字段
     * 3. 验证表格内容展示
     */
    test('表格字段展示', async () => {
      await lecturerPage.open();
      // 检查表头字段
      const headers = [
        '讲师ID', '讲师姓名', '手机号', '所在地区', '所属机构', '坐诊医院',
        '所在科室', '职称', '讲师分类', '讲师账号', '创建人', '创建时间'
      ];
      for (const header of headers) {
        await expect(lecturerPage.locator(`.datatable-header-cell-label:has-text("${header}")`)).toBeVisible();
      }
    });

    /**
     * 测试用例：编辑讲师
     * 步骤：
     * 1. 打开讲师管理页面
     * 2. 搜索要编辑的讲师
     * 3. 点击编辑按钮
     * 4. 修改讲师信息
     * 5. 点击保存按钮
     * 6. 验证讲师是否编辑成功
     */
    test('编辑讲师', async () => {
      // 执行编辑讲师操作
      await lecturerPage.edit({ lecturerName: testData.basicLecturerData.lecturerName }, testData.editLecturerData);
      // 验证讲师是否编辑成功
      await lecturerPage.search({ lecturerName: testData.editLecturerData.lecturerName });
             await expect(lecturerPage.locator('datatable-body-row')).toBeVisible();
       await expect(lecturerPage.locator('datatable-body-row')).toContainText(testData.editLecturerData.lecturerName || '');
    });

    /**
     * 测试用例：查看讲师详情
     * 步骤：
     * 1. 打开讲师管理页面
     * 2. 搜索要查看的讲师
     * 3. 点击详情按钮
     * 4. 验证详情页面内容
     */
    test('查看讲师详情', async () => {
      // 执行查看讲师详情操作
      await lecturerPage.detail({ lecturerName: testData.editLecturerData.lecturerName });
      // 验证详情页面内容
      const detailDialog = lecturerPage.locator('.modal-dialog');
      await expect(detailDialog).toBeVisible();
      // TODO: 根据实际详情页面内容进行更详细的验证
    });

    /**
     * 测试用例：讲师配置
     * 步骤：
     * 1. 打开讲师管理页面
     * 2. 搜索要配置的讲师
     * 3. 点击配置按钮
     * 4. 验证配置页面
     */
    test('讲师配置', async () => {
      // 执行讲师配置操作
      await lecturerPage.config({ lecturerName: testData.editLecturerData.lecturerName });
      // TODO: 根据实际配置页面内容进行验证
    });

    /**
     * 测试用例：关联账号
     * 步骤：
     * 1. 打开讲师管理页面
     * 2. 搜索要关联账号的讲师
     * 3. 点击关联账号按钮
     * 4. 验证关联账号页面
     */
    test('关联账号', async () => {
      // 执行关联账号操作
      await lecturerPage.relateAccount({ lecturerName: testData.editLecturerData.lecturerName });
      // TODO: 根据实际关联账号页面内容进行验证
    });

    /**
     * 测试用例：操作按钮功能
     * 步骤：
     * 1. 打开讲师管理页面
     * 2. 验证新增讲师按钮
     * 3. 验证列表操作按钮（编辑、配置、关联账号）
     */
         test('操作按钮功能', async () => {
       await lecturerPage.open();
       // 验证新增讲师按钮
       await expect(lecturerPage.locator('button:has-text("新增讲师")')).toBeVisible();
       
       // 如果有数据，验证列表操作按钮
       const hasData = await lecturerPage.locator('datatable-body-row').isVisible();
       if (hasData) {
         await expect(lecturerPage.locator('a:has-text("编辑")').first()).toBeVisible();
         await expect(lecturerPage.locator('a:has-text("配置")').first()).toBeVisible();
         await expect(lecturerPage.locator('a:has-text("关联账号")').first()).toBeVisible();
       }
     });

         /**
      * 测试用例：高级搜索功能
      * 步骤：
      * 1. 打开讲师管理页面
      * 2. 填写下拉选择框搜索条件
      * 3. 点击查询按钮
      * 4. 验证搜索结果
      */
     test('高级搜索功能', async () => {
       await lecturerPage.open();
       
       // 测试地区选择
       if (testData.searchData.region) {
         await lecturerPage.selectRegion(testData.searchData.region);
       }
       
       // 测试机构选择
       if (testData.searchData.organization) {
         await lecturerPage.selectOrganization(testData.searchData.organization);
       }
       
       // 点击查询
       await lecturerPage.locator('.btn-search:has-text("查询")').click();
       
       // 验证搜索结果
       await expect(lecturerPage.locator('datatable-body-row')).toBeVisible();
     });

     /**
      * 测试用例：分页功能
      * 步骤：
      * 1. 打开讲师管理页面
      * 2. 验证分页控件
      * 3. 测试分页跳转功能
      */
     test('分页功能', async () => {
       await lecturerPage.open();
       // 验证分页控件存在
       await expect(lecturerPage.locator('.datatable-pager')).toBeVisible();
       // 验证分页信息显示
       await expect(lecturerPage.locator('.page-count')).toBeVisible();
       // 验证每页显示数量选择
       await expect(lecturerPage.locator('.page-size-continer')).toBeVisible();
       // TODO: 如果有多页数据，可以测试翻页功能
     });

    /**
     * 测试用例：删除讲师
     * 步骤：
     * 1. 打开讲师管理页面
     * 2. 搜索要删除的讲师
     * 3. 点击删除按钮
     * 4. 确认删除
     * 5. 验证讲师是否删除成功
     */
    test('删除讲师', async () => {
      // 执行删除讲师操作
      await lecturerPage.delete({ lecturerName: testData.editLecturerData.lecturerName });
      // 验证讲师是否删除成功
      await lecturerPage.search({ lecturerName: testData.editLecturerData.lecturerName });
      await expect(lecturerPage.locator('datatable-body-row')).not.toBeVisible();
    });
  });
}); 