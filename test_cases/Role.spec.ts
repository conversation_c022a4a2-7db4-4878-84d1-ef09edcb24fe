import { test, expect } from '@playwright/test';
import { RolePage } from '../test_pages/RolePage';
import { getCurrentConfig } from '../config';
import { BrowserManager } from '../utils/utils';
import * as testData from '../test_datas/RoleData';

const config = getCurrentConfig();

// 配置测试用例为串行执行，确保测试用例按顺序执行
test.describe.configure({ mode: 'serial' });

test.describe('角色管理', () => {
  // 定义全局变量
  let rolePage: RolePage;
  let browserManager: BrowserManager;
  let roleId: string;

  test.beforeAll(async ({ browser }) => {
    // 初始化浏览器管理器
    browserManager = BrowserManager.getInstance();
    // 创建角色管理页面实例，使用管理员权限
    rolePage = await browserManager.createPage(browser, config.STORAGE.ADMIN_STORAGE, RolePage);
  });

  test.describe('ZYWZ-059 角色基本操作', () => {
    /**
     * 测试用例：空数据保存
     * 步骤：
     * 1. 打开角色管理页面
     * 2. 点击新增角色按钮
     * 3. 不填写任何数据
     * 4. 点击保存按钮
     * 5. 验证错误提示
     */
    test('空数据保存', async () => {
      // 执行新增角色操作
      await rolePage.add(testData.emptyData);
      // 验证错误提示
      await expect(rolePage.getByRole('alertdialog')).toContainText('表单数据不完整，请检查');
      await rolePage.cancel();
    });

    /**
     * 测试用例：添加完整字段角色
     * 步骤：
     * 1. 打开角色管理页面
     * 2. 点击新增角色按钮
     * 3. 填写所有字段
     * 4. 点击保存按钮
     * 5. 验证角色是否创建成功
     */
    test('添加完整字段角色', async () => {
      // 执行新增角色操作
      await rolePage.add(testData.basicRoleData);
      // 验证角色是否创建成功
      await rolePage.search({ roleName: testData.basicRoleData.roleName });
      await expect(rolePage.locator('datatable-body-row')).toBeVisible();
      await expect(rolePage.locator('datatable-body-row')).toContainText(testData.basicRoleData.roleName);
    });

    /**
     * 测试用例：编辑角色
     * 步骤：
     * 1. 打开角色管理页面
     * 2. 搜索要编辑的角色
     * 3. 点击编辑按钮
     * 4. 修改角色信息
     * 5. 点击保存按钮
     * 6. 验证角色是否编辑成功
     */
    test('编辑角色', async () => {
      // 执行编辑角色操作
      await rolePage.edit({ roleName: testData.basicRoleData.roleName }, testData.editRoleData);
      // 验证角色是否编辑成功
      await rolePage.search({ roleName: testData.editRoleData.roleName });
      await expect(rolePage.locator('datatable-body-row')).toBeVisible();
      await expect(rolePage.locator('datatable-body-row')).toContainText(testData.editRoleData.roleName);
    });

    /**
     * 测试用例：查看角色详情
     * 步骤：
     * 1. 打开角色管理页面
     * 2. 搜索要查看的角色
     * 3. 点击详情按钮
     * 4. 验证详情页面内容
     */
    test('查看角色详情', async () => {
      // 执行查看角色详情操作
      await rolePage.detail({ roleName: testData.editRoleData.roleName });
      // 验证详情页面内容
      const detailDialog = rolePage.locator('app-show-role');
      await expect(detailDialog).toBeVisible();
      await expect(detailDialog).toContainText(testData.editRoleData.roleName);
      await expect(detailDialog).toContainText(testData.editRoleData.dataPermission);
    });

    /**
     * 测试用例：角色授权
     * 步骤：
     * 1. 打开角色管理页面
     * 2. 搜索要授权的角色
     * 3. 点击授权按钮
     * 4. 验证授权页面内容
     */
    test('角色授权', async () => {
      // 执行角色授权操作
      await rolePage.authorization({ roleName: testData.editRoleData.roleName });
      await expect(rolePage.getByRole('alertdialog')).toContainText('权限添加成功！');
      await rolePage.close();

      // 执行查看角色详情操作
      await rolePage.detail({ roleName: testData.editRoleData.roleName });
      const detailDialog = rolePage.locator('app-show-role'); 
      await expect(detailDialog).toContainText('工作台 挂号 就诊 收费');

      

    });

    /**
     * 测试用例：删除角色
     * 步骤：
     * 1. 打开角色管理页面
     * 2. 搜索要删除的角色
     * 3. 点击删除按钮
     * 4. 确认删除
     * 5. 验证角色是否删除成功
     */
    test('删除角色', async () => {
      // 执行删除角色操作
      await rolePage.delete({ roleName: testData.editRoleData.roleName });
      // 验证角色是否删除成功
      await rolePage.search({ roleName: testData.editRoleData.roleName });
      await expect(rolePage.locator('datatable-body-row')).not.toBeVisible();
    });
  });
});
