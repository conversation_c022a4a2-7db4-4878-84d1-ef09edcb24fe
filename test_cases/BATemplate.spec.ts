

import { test, expect } from '@playwright/test';
import { BATemplatePage } from '../test_pages/BATemplatePage';
import { getCurrentConfig } from '../config';
import { BrowserManager } from '../utils/utils';
import * as testData from '../test_datas/BATemplateData';

const config = getCurrentConfig();

// 配置测试用例为串行执行，确保测试用例按顺序执行
test.describe.configure({ mode: 'serial' });

// 定义全局变量
let baTemplatePage: BATemplatePage;
let browserManager: BrowserManager;


test.describe('病案模板管理', () => {

  // 在所有测试用例执行前初始化浏览器管理器和页面对象
  test.beforeAll(async ({ browser }) => {
    // 初始化浏览器管理器
    browserManager = BrowserManager.getInstance();
    // 创建模板管理页面实例，使用医师权限
    baTemplatePage = await browserManager.createPage(browser, config.STORAGE.DOCTOR_STORAGE, BATemplatePage);
  });

  // 完整模板测试套件
  test.describe('ZYWZ-383 新增模板操作', () => {

    /**
     * 测试用例：空数据保存
     * 步骤：
     * 1. 打开病案模板管理页面
     * 2. 点击新增模板按钮
     * 3. 不填写任何数据
     * 4. 点击确定按钮
     * 5. 验证错误提示
     * 6. 取消操作
     */
    test('空数据保存', async () => {
      // 执行新增模板操作
      await baTemplatePage.add(testData.emptyData);
      // 验证错误提示
      await expect(baTemplatePage.getByRole('alertdialog')).toContainText('信息填写不完整，请检查');
      await baTemplatePage.cancel();
    });

    /**
     * 测试用例：必填字段保存
     * 步骤：
     * 1. 打开病案模板管理页面
     * 2. 点击新增模板按钮
     * 3. 只填写必填字段（模板名称、模板类型、诊断、说明）
     * 4. 点击确定按钮
     * 5. 验证模板是否创建成功
     */
    test('必填字段保存', async () => {
      // 执行新增模板操作
      await baTemplatePage.add(testData.reqAddData);
      // 验证模板是否创建成功
      await expect(baTemplatePage.locator('datatable-scroller')).toContainText(testData.reqAddData.name);
    });


    /**
     * 测试用例：填写已存在的模板名称
     * 步骤：
     * 1. 打开病案模板管理页面
     * 2. 点击新增模板按钮
     * 3. 填写与已有模板相同的名称
     * 4. 点击确定按钮
     * 5. 验证错误提示
     * 6. 取消操作
     */
    test('填写已存在的模板名称', async () => {
      // 执行新增模板操作
      await baTemplatePage.add(testData.reqAddData);
      // 验证错误提示
      await expect(baTemplatePage.getByRole('alertdialog')).toContainText('保存失败。模版名称重复，请检查');
      await baTemplatePage.cancel();
    });

    /**
     * 测试用例：完整字段保存
     * 步骤：
     * 1. 打开病案模板管理页面
     * 2. 点击新增模板按钮
     * 3. 填写所有字段（包括必填和非必填）
     * 4. 点击确定按钮
     * 5. 验证模板是否创建成功
     */
    test('完整字段保存', async () => {
      // 执行新增模板操作
      await baTemplatePage.add(testData.fullAddData);
      // 验证模板是否创建成功
      await expect(baTemplatePage.locator('datatable-scroller')).toContainText(testData.fullAddData.name);
    });
  });

  // 必填项模板测试套件
  test.describe('ZYWZ-386 编辑模板操作', () => {
    /**
     * 测试用例：清空表单内容保存
     * 步骤：
     * 1. 打开病案模板管理页面
     * 2. 搜索要编辑的模板
     * 3. 点击编辑按钮
     * 4. 清空所有字段
     * 5. 点击确定按钮
     * 6. 验证错误提示
     * 7. 取消操作
     */
    test('清空表单内容保存', async () => {
      // 执行编辑模板操作
      await baTemplatePage.edit({name: testData.fullAddData.name }, testData.emptyData);
      // 验证错误提示
      await expect(baTemplatePage.getByRole('alertdialog')).toContainText('信息填写不完整，请检查');
      await baTemplatePage.cancel();
    });

    /**
     * 测试用例：清空模板内容保存
     * 步骤：
     * 1. 打开病案模板管理页面
     * 2. 搜索要编辑的模板
     * 3. 点击编辑按钮
     * 4. 只保留必填字段，清空其他内容
     * 5. 点击确定按钮
     * 6. 清空搜索条件
     * 7. 验证模板是否编辑成功
     */
    test('清空模板内容保存', async () => {
      // 执行编辑模板操作
      await baTemplatePage.edit({name: testData.fullAddData.name }, testData.reqEditData);
      await baTemplatePage.clearSearch();
      // 验证模板是否编辑成功
      await expect(baTemplatePage.locator('datatable-scroller')).toContainText(testData.reqEditData.name);
      await expect(baTemplatePage.locator('datatable-scroller')).toContainText(testData.reqEditData.diagnosis);
      await expect(baTemplatePage.locator('datatable-scroller')).toContainText(testData.reqEditData.description);
    });

    /**
     * 测试用例：填写已存在的模板名称保存
     * 步骤：
     * 1. 打开病案模板管理页面
     * 2. 搜索要编辑的模板
     * 3. 点击编辑按钮
     * 4. 修改模板名称为已存在的名称
     * 5. 点击确定按钮
     * 6. 验证错误提示
     * 7. 取消操作
     */
    test('填写已存在的模板名称保存', async () => {
      // 执行编辑模板操作
      await baTemplatePage.edit({name: testData.reqEditData.name }, testData.reqAddData);
      // 验证错误提示
      await expect(baTemplatePage.getByRole('alertdialog')).toContainText('保存失败。模版名称重复，请检查');
      await baTemplatePage.cancel();
    });

    /**
     * 测试用例：修改所有字段内容保存
     * 步骤：
     * 1. 打开病案模板管理页面
     * 2. 搜索要编辑的模板
     * 3. 点击编辑按钮
     * 4. 修改所有字段内容
     * 5. 点击确定按钮
     * 6. 查看模板详情
     * 7. 验证所有字段是否修改成功
     */
    test('修改所有字段内容保存', async () => {
      // 执行编辑模板操作
      await baTemplatePage.edit({name: testData.reqEditData.name }, testData.fullEditData);
      // 验证模板是否编辑成功
      await baTemplatePage.detail({name: testData.fullEditData.name });

      // 验证模板详情页面内容
      const detailDialog = baTemplatePage.locator('.modal-dialog');

      // 验证对话框中是否包含所有测试数据
      await expect(detailDialog).toContainText(testData.fullEditData.name);
      await expect(detailDialog).toContainText(testData.fullEditData.type);
      await expect(detailDialog).toContainText(testData.fullEditData.diagnosis);
      await expect(detailDialog).toContainText(testData.fullEditData.description);

      // 验证模板内容
      await expect(detailDialog).toContainText(testData.fullEditData.chiefComplaint || '');
      await expect(detailDialog).toContainText(testData.fullEditData.physicalExam || '');
      await expect(detailDialog).toContainText(testData.fullEditData.presentIllness || '');
      await expect(detailDialog).toContainText(testData.fullEditData.pastHistory || '');
      await expect(detailDialog).toContainText(testData.fullEditData.personalHistory || '');
      await expect(detailDialog).toContainText(testData.fullEditData.familyHistory || '');
      await expect(detailDialog).toContainText(testData.fullEditData.allergicHistory || '');
      await expect(detailDialog).toContainText(testData.fullEditData.marriageHistory || '');
      await expect(detailDialog).toContainText(testData.fullEditData.symptoms || '');
    });



  });
});
